import { logger } from "../../../helpers/logger.js";
import { getServiceContainer } from "../../../service/container/service-container.js";
import { prepareMerchantDataForPlaid } from "./plaid-verification.service.js";
import type { IPayrixMerchantCreationService } from "../../../service/interfaces/payrix-service.interfaces.js";
import type { OnboardingRequest } from "../schemas/onboarding.schema.js";

export interface MerchantCreationResult {
  success: boolean;
  merchantId?: string;
  merchantData?: { id: string; [key: string]: unknown };
  error?: string;
}

export interface MerchantCreationOrchestratorServiceDependencies {
  merchantCreationService?: IPayrixMerchantCreationService;
}

// Direct export with optional dependencies
export function createMerchantWithPreparationAndDependencies(
  data: OnboardingRequest,
  requestId: string,
  dependencies: MerchantCreationOrchestratorServiceDependencies = {}
): Promise<MerchantCreationResult> {
  const serviceContainer = getServiceContainer();
  const merchantCreationService = dependencies.merchantCreationService || serviceContainer.getMerchantCreationService();
  return createMerchantWithPreparation(merchantCreationService, data, requestId);
}

async function createMerchantWithPreparation(
  merchantCreationService: IPayrixMerchantCreationService,
  data: OnboardingRequest,
  requestId: string
): Promise<MerchantCreationResult> {
  try {
    const merchantData = prepareMerchantData(data, requestId);

    logger.info("Creating merchant in Payrix (direct integration)", { requestId });
    const payrixResponse = await merchantCreationService.createMerchant(merchantData);
    const payrixEntityId = payrixResponse.id;

    if (!payrixEntityId) {
      throw new Error("Payrix response did not contain entity ID");
    }

    logger.info("Payrix merchant created successfully (direct integration)", {
      requestId,
      payrixEntityId,
    });

    return {
      success: true,
      merchantId: payrixEntityId,
      merchantData: payrixResponse,
    };
  } catch (error) {
    logger.error("Merchant creation failed", {
      requestId,
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
    });

    return {
      success: false,
      error: error instanceof Error ? error.message : "Merchant creation failed",
    };
  }
}

function prepareMerchantData(data: OnboardingRequest, requestId: string): OnboardingRequest {
  let merchantData = { ...data };

  if (data.bankVerification?.verificationMethod === "plaid" && data.bankVerification.plaidData) {
    merchantData = prepareMerchantDataForPlaid(merchantData, data.bankVerification.plaidData, requestId);
  }

  return merchantData;
}

// Function is now directly exported above
