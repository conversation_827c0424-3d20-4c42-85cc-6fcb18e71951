import { logger } from "../../../helpers/logger.js";
import { createUserAccountIfRequested } from "./user-account.service.js";
import type { OnboardingRequest } from "../schemas/onboarding.schema.js";

export interface UserAccountCreationResult {
  success: boolean;
  data?: {
    id: string;
    email: string;
    sanitizedUsername?: string;
    originalUsername?: string;
    [key: string]: unknown;
  } | null;
  error?: string;
}

// Direct export of the function - no wrapper needed

export async function createUserAccountWithErrorHandling(
  data: OnboardingRequest,
  payrixEntityId: string,
  requestId: string
): Promise<UserAccountCreationResult> {
  try {
    logger.info("Processing user account creation (before bank verification)", {
      requestId,
      payrixEntityId,
    });

    const result = await createUserAccountIfRequested(data, payrixEntityId, requestId);

    if (result.success) {
      logger.info("User account created successfully - login context available for bank verification", {
        requestId,
        payrixEntityId,
        userId: result.data?.id,
        hasLoginId: !!result.data?.id,
      });
    } else {
      logger.warn("User account creation failed - bank verification will proceed without login context", {
        requestId,
        payrixEntityId,
        error: result.error,
        impact: "Bank verification notes will be created without user login context",
      });
    }

    return result;
  } catch (userAccountError) {
    logger.error("User account creation failed with exception - bank verification will proceed without login context", {
      requestId,
      payrixEntityId,
      error: userAccountError,
      impact: "Bank verification notes will be created without user login context",
    });

    return {
      success: false,
      error: "User account creation failed",
    };
  }
}

// Function is now directly exported above
