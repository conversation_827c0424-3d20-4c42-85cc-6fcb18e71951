import { APIGatewayProxyHandler } from "aws-lambda";
import { payrixService } from "../../service/payrix.service.js";
import { createErrorResponse } from "../../helpers/response.js";
import { logger } from "../../helpers/logger.js";
import { z } from "zod";
import multipart from "lambda-multipart-parser";

const CreateNoteDocumentRequestSchema = z.object({
  noteId: z.string().min(1, "Note ID is required"),
  description: z.string().optional(),
});

export const handler: APIGatewayProxyHandler = async (event) => {
  const requestId = event.requestContext.requestId;

  try {
    logger.info("Processing create note document request", { requestId });

    if (!event.body) {
      return createErrorResponse(400, "Request body is required", requestId);
    }

    // Parse multipart form data
    let parsedData;
    try {
      parsedData = await multipart.parse(event);
    } catch (parseError) {
      logger.error("Failed to parse multipart data", { requestId, error: parseError });
      return createErrorResponse(400, "Invalid multipart data", requestId);
    }

    // Validate required fields
    const validationResult = CreateNoteDocumentRequestSchema.safeParse({
      noteId: parsedData.noteId,
      description: parsedData.description,
    });

    if (!validationResult.success) {
      const errors = validationResult.error.errors.map((err) => `${err.path.join(".")}: ${err.message}`);
      logger.error("Validation failed", { requestId, errors });
      return createErrorResponse(400, `Validation failed: ${errors.join(", ")}`, requestId);
    }

    const { noteId, description } = validationResult.data;

    // Check if file was uploaded
    if (!parsedData.files || parsedData.files.length === 0) {
      return createErrorResponse(400, "File is required", requestId);
    }

    const file = parsedData.files[0];

    // Validate file type (should be image or PDF for void check)
    const allowedMimeTypes = ["image/jpeg", "image/jpg", "image/png", "application/pdf"];
    if (!allowedMimeTypes.includes(file.contentType)) {
      return createErrorResponse(400, "File must be an image (JPEG, PNG) or PDF", requestId);
    }

    // Validate file size (max 10MB)
    const maxSizeBytes = 10 * 1024 * 1024; // 10MB
    if (file.content.length > maxSizeBytes) {
      return createErrorResponse(400, "File size must be less than 10MB", requestId);
    }

    // Use the functional service instance

    // Create note document via Payrix API
    const documentData = {
      note: noteId,
      file: {
        filename: file.filename,
        content: file.content,
        contentType: file.contentType,
      },
      description: description || "Void check for bank account verification",
    };

    logger.info("Creating note document in Payrix", {
      requestId,
      noteId,
      fileName: file.filename,
      fileSize: file.content.length,
      contentType: file.contentType,
    });

    const payrixResponse = await payrixService.createNoteDocument(documentData);

    if (!payrixResponse?.id) {
      throw new Error("Payrix response did not contain document ID");
    }

    logger.info("Note document created successfully", {
      requestId,
      documentId: payrixResponse.id,
      noteId,
      fileName: file.filename,
    });

    return {
      statusCode: 200,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
      },
      body: JSON.stringify({
        success: true,
        message: "Note document created successfully",
        data: {
          documentId: payrixResponse.id,
          noteId,
          fileName: file.filename,
          fileSize: file.content.length,
          mimeType: file.contentType,
          description: description || "Void check for bank account verification",
          uploadedAt: new Date().toISOString(),
        },
      }),
    };
  } catch (error) {
    logger.error("Error creating note document", { requestId, error });

    // Provide specific error messages based on error type
    let errorMessage = "Internal server error";
    let statusCode = 500;

    if (error instanceof Error) {
      if (error.message.includes("Payrix")) {
        errorMessage = "Failed to upload document to payment processor. Please try again.";
        statusCode = 502; // Bad Gateway
      } else if (error.message.includes("timeout")) {
        errorMessage = "Upload timeout. Please try again with a smaller file.";
        statusCode = 408; // Request Timeout
      } else if (error.message.includes("File")) {
        errorMessage = error.message; // Pass through file validation errors
        statusCode = 400; // Bad Request
      } else if (error.message.includes("Note")) {
        errorMessage = "Invalid note reference. Please try again.";
        statusCode = 400; // Bad Request
      }
    }

    return createErrorResponse(statusCode, errorMessage, requestId);
  }
};
