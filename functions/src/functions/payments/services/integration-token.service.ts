import { randomBytes } from "crypto";
import { setToken } from "../../../service/token-storage.js";

export function generateSecureToken(): string {
  return randomBytes(32).toString("hex");
}

// Direct exports - no wrappers needed
export { validateToken, markTokenAsUsed } from "../../../service/token-storage.js";

export async function storeToken(
  token: string,
  data: {
    merchantId: string;
    description: string;
    amount: number;
    returnUrl?: string;
    expiresAt: Date;
    used: boolean;
    currency?: string;
    items?: Array<{
      name: string;
      description?: string;
      quantity: number;
      unitPrice: number;
      total: number;
      commodityCode?: string;
      productCode?: string;
    }>;
    taxAmount?: number;
    shippingAmount?: number;
    dutyAmount?: number;
    orderNumber?: string;
    invoiceNumber?: string;
    customerCode?: string;
    orderDiscount?: number;
    merchantInfo?: {
      address?: {
        line1?: string;
        line2?: string;
        city?: string;
        state?: string;
        zip?: string;
        country?: string;
      };
      contactEmail?: string;
      contactPhone?: string;
    };
  }
): Promise<void> {
  await setToken(token, data);
}
