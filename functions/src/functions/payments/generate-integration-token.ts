import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { z } from "zod";
import { createIframeResponse } from "../../helpers/response.js";
import { logger } from "../../helpers/logger.js";
import { sanitizeInput, withIframeSecurity } from "../../middleware/security.js";
import { payrixService } from "../../service/payrix.service.js";
import { IntegrationTokenRequest } from "../../types/integration-token.types.js";
import { tokenRequestSchema } from "./schemas/integration-token.schema.js";
import {
  generateSecureToken,
  storeToken,
  validateToken as validateTokenService,
  markTokenAsUsed as markTokenAsUsedService,
} from "./services/integration-token.service.js";
import { validateIntegrationTokenRequest } from "./validators/integration-token.validator.js";
import { buildTokenData, buildTokenResponse } from "./utils/token-config-builder.js";

interface MerchantAddress {
  line1?: string;
  line2?: string;
  city?: string;
  state?: string;
  zip?: string;
  country?: string;
}

interface PayrixMerchant {
  dba?: string;
  name?: string;
  status?: number;
  address?: MerchantAddress;
  email?: string;
  phone?: string;
}

export const validateToken = validateTokenService;
export const markTokenAsUsed = markTokenAsUsedService;

const handlerImpl = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    logger.info("Generate integration token request", {
      method: event.httpMethod,
      path: event.path,
      hasBody: !!event.body,
      contentType: event.headers?.["content-type"] || event.headers?.["Content-Type"],
    });

    if (!event.body) {
      return createIframeResponse(400, {
        error: "Request body is required",
        message: "Please provide merchantId and description",
      });
    }

    let requestData: IntegrationTokenRequest;
    try {
      const parsedBody = JSON.parse(event.body);
      const validatedData = tokenRequestSchema.parse(parsedBody);
      requestData = validatedData as IntegrationTokenRequest;
    } catch (error) {
      if (error instanceof z.ZodError) {
        return createIframeResponse(400, {
          error: "Validation failed",
          message: "Invalid request data",
          details: error.errors,
        });
      }
      throw error;
    }

    const {
      merchantId,
      description,
      amount = 1000,
      returnUrl,
      expiresIn = 60,
      currency = "USD",
      items,
      taxAmount,
      shippingAmount,
      dutyAmount,
      orderNumber,
      invoiceNumber,
      customerCode,
      orderDiscount,
    } = requestData;

    const validationErrors = validateIntegrationTokenRequest(requestData);
    if (validationErrors.length > 0) {
      const firstError = validationErrors[0];
      return createIframeResponse(firstError.statusCode || 400, {
        error: firstError.error,
        message: firstError.message,
        details: firstError.details,
      });
    }

    const sanitizedDescription = sanitizeInput(description);

    logger.info("Processing integration token request", {
      merchantId,
      description: sanitizedDescription,
      amount,
      returnUrl,
      expiresIn,
    });

    // Use the functional service instance
    const validation = await payrixService.validateMerchantById(merchantId);

    if (!validation.isValid) {
      logger.warn("Merchant validation failed", {
        merchantId,
        error: validation.error,
      });

      return createIframeResponse(404, {
        error: "Merchant validation failed",
        message: validation.error || "Invalid or inactive merchant",
        details: {
          merchantId,
          validationError: validation.error,
        },
      });
    }

    const merchant = validation.merchant as PayrixMerchant;

    logger.info("Merchant validation successful, generating integration token", {
      merchantId,
      merchantName: merchant?.dba || merchant?.name,
    });

    const token = generateSecureToken();
    const expiresAt = new Date(Date.now() + expiresIn * 60 * 1000);

    const tokenData = buildTokenData(
      merchantId,
      sanitizedDescription,
      amount,
      expiresAt,
      returnUrl,
      {
        currency,
        items,
        taxAmount,
        shippingAmount,
        dutyAmount,
        orderNumber,
        invoiceNumber,
        customerCode,
        orderDiscount,
      },
      merchant?.address || merchant?.email || merchant?.phone
        ? {
            address: merchant?.address
              ? {
                  line1: merchant.address.line1,
                  line2: merchant.address.line2,
                  city: merchant.address.city,
                  state: merchant.address.state,
                  zip: merchant.address.zip,
                  country: merchant.address.country || "US",
                }
              : undefined,
            email: merchant?.email,
            phone: merchant?.phone,
          }
        : undefined
    );

    await storeToken(token, tokenData);

    const response = buildTokenResponse(token, expiresAt, {
      id: merchantId,
      name: merchant?.dba || merchant?.name,
      status: merchant?.status,
    });

    logger.info("Integration token generated successfully", {
      token: token.substring(0, 8) + "...",
      expiresAt: expiresAt.toISOString(),
      merchantId,
    });

    return createIframeResponse(200, {
      success: true,
      message: "Integration token generated successfully",
      data: response,
    });
  } catch (error) {
    logger.error("Error generating integration token", { error });

    return createIframeResponse(500, {
      error: "Internal server error",
      message: "Failed to generate integration token",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

export const handler = withIframeSecurity(handlerImpl);
