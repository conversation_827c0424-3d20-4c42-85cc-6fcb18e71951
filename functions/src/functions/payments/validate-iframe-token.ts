import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { createIframeResponse } from "../../helpers/response.js";
import { logger } from "../../helpers/logger.js";
import { payrixService } from "../../service/payrix.service.js";
import { validateToken } from "./generate-integration-token";
import { withIframeSecurity, validateTokenFormat } from "../../middleware/security.js";
import { z } from "zod";

interface TokenValidationRequest {
  token: string;
}

interface PayFieldsConfig {
  merchantId: string;
  publicKey: string;
  amount: number;
  description: string;
  mode: "txn" | "txnToken" | "token";
  txnType: "sale" | "auth" | "ecsale";
}

interface MerchantInfo {
  id: string;
  name: string;
  status: number;
  address?: {
    line1?: string;
    line2?: string;
    city?: string;
    state?: string;
    zip?: string;
    country?: string;
  };
  contactEmail?: string;
  contactPhone?: string;
}

const tokenValidationSchema = z.object({
  token: z.string().min(1, "Token is required"),
});

const handlerImpl = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    logger.info("Validate iframe token request", {
      body: event.body,
      headers: event.headers,
    });

    if (!event.body) {
      return createIframeResponse(400, {
        error: "Request body is required",
        message: "Please provide a token",
      });
    }

    let requestData: TokenValidationRequest;
    try {
      requestData = tokenValidationSchema.parse(JSON.parse(event.body));
    } catch (error) {
      if (error instanceof z.ZodError) {
        return createIframeResponse(400, {
          error: "Validation failed",
          message: "Invalid request data",
          details: error.errors,
        });
      }
      throw error;
    }

    const { token } = requestData;
    const tokenFormatValidation = validateTokenFormat(token);
    if (!tokenFormatValidation.isValid) {
      return createIframeResponse(tokenFormatValidation.statusCode || 400, {
        error: tokenFormatValidation.error,
        message: "Invalid token format",
      });
    }

    logger.info("Processing iframe token validation", {
      token: token.substring(0, 8) + "...",
    });

    const tokenValidation = await validateToken(token);

    if (!tokenValidation.isValid || !tokenValidation.data) {
      logger.warn("Token validation failed", {
        token: token.substring(0, 8) + "...",
        error: tokenValidation.error,
      });

      return createIframeResponse(401, {
        error: "Token validation failed",
        message: tokenValidation.error || "Invalid or expired token",
      });
    }

    const {
      merchantId,
      description,
      amount,
      returnUrl,
      currency,
      items,
      taxAmount,
      shippingAmount,
      dutyAmount,
      orderNumber,
      invoiceNumber,
      customerCode,
      orderDiscount,
      merchantInfo: tokenMerchantInfo,
    } = tokenValidation.data;

    logger.info("Token validation successful, validating merchant", {
      merchantId,
      description,
      amount,
    });

    // Use the functional service instance
    const merchantValidation = await payrixService.validateMerchantById(merchantId);

    if (!merchantValidation.isValid) {
      logger.warn("Merchant validation failed", {
        merchantId,
        error: merchantValidation.error,
      });
      return createIframeResponse(404, {
        error: "Merchant validation failed",
        message: merchantValidation.error || "Invalid or inactive merchant",
        details: {
          merchantId,
          validationError: merchantValidation.error,
        },
      });
    }

    logger.info("Merchant validation successful, generating PayFields config", {
      merchantId,
      merchantName: merchantValidation.merchant?.dba || merchantValidation.merchant?.name,
    });

    const config: PayFieldsConfig = {
      merchantId,
      publicKey: process.env.PAYRIX_PUBLIC_API_KEY || "default-public-key",
      amount: 0, // $0 authorization for tokenization
      description,
      mode: "token", // Token-only mode for PCI compliance
      txnType: "auth", // Authorization for tokenization
    };

    const merchant = merchantValidation.merchant as Record<string, unknown> | undefined;
    const merchantAddress = merchant?.address as Record<string, unknown> | undefined;

    const merchantInfo: MerchantInfo = {
      id: merchantId,
      name: (merchant?.dba as string) || (merchant?.name as string) || "Unknown",
      status: (merchant?.status as number) || 0,
      address:
        tokenMerchantInfo?.address ||
        (merchantAddress
          ? {
              line1: merchantAddress.line1 as string,
              line2: merchantAddress.line2 as string,
              city: merchantAddress.city as string,
              state: merchantAddress.state as string,
              zip: merchantAddress.zip as string,
              country: (merchantAddress.country as string) || "US",
            }
          : undefined),
      contactEmail: tokenMerchantInfo?.contactEmail || (merchant?.email as string),
      contactPhone: tokenMerchantInfo?.contactPhone || (merchant?.phone as string),
    };

    logger.info("Iframe token validation and config generation successful", {
      token: token.substring(0, 8) + "...",
      merchantId,
      amount,
      description,
    });

    return createIframeResponse(200, {
      success: true,
      message: "Token validated and payment configuration generated successfully",
      data: {
        config,
        merchantInfo,
        paymentInfo: {
          description,
          amount,
          currency,
          returnUrl,
          items,
          taxAmount,
          shippingAmount,
          dutyAmount,
          orderNumber,
          invoiceNumber,
          customerCode,
          orderDiscount,
        },
      },
    });
  } catch (error) {
    logger.error("Error validating iframe token", { error });

    return createIframeResponse(500, {
      error: "Internal server error",
      message: "Failed to validate token and generate payment configuration",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

export const handler = withIframeSecurity(handlerImpl);
