import { createPayrixMerchantValidationService } from "../payrix/merchant-validation.service.js";
import { createPayrixMerchantCreationService } from "../payrix/merchant-creation.service.js";
import { createPayrixNoteManagementService } from "../payrix/note-management.service.js";
import { createPayrixUserAccount } from "../payrix/user.service.js";
import { createPayrixPaymentService } from "../payrix/payment.service.js";
import { createPayrixMerchantService } from "../payrix/merchant.service.js";
import type {
  IPayrixMerchantValidationService,
  IPayrixMerchantCreationService,
  IPayrixNoteManagementService,
  IPayrixUserService,
  IPayrixPaymentService,
  IPayrixMerchantService,
} from "../interfaces/payrix-service.interfaces.js";

export interface ServiceRegistry {
  merchantValidationService: IPayrixMerchantValidationService;
  merchantCreationService: IPayrixMerchantCreationService;
  noteManagementService: IPayrixNoteManagementService;
  userService: IPayrixUserService;
  paymentService: IPayrixPaymentService;
  merchantService: IPayrixMerchantService;
}

export interface ServiceContainer {
  getMerchantValidationService(): IPayrixMerchantValidationService;
  getMerchantCreationService(): IPayrixMerchantCreationService;
  getNoteManagementService(): IPayrixNoteManagementService;
  getUserService(): IPayrixUserService;
  getPaymentService(): IPayrixPaymentService;
  getMerchantService(): IPayrixMerchantService;
  setServices(services: Partial<ServiceRegistry>): void;
  reset(): void;
}

type ServiceFactory<T> = () => T;

interface ServiceFactories {
  merchantValidationService: ServiceFactory<IPayrixMerchantValidationService>;
  merchantCreationService: ServiceFactory<IPayrixMerchantCreationService>;
  noteManagementService: ServiceFactory<IPayrixNoteManagementService>;
  userService: ServiceFactory<IPayrixUserService>;
  paymentService: ServiceFactory<IPayrixPaymentService>;
  merchantService: ServiceFactory<IPayrixMerchantService>;
}

const defaultServiceFactories: ServiceFactories = {
  merchantValidationService: () => createPayrixMerchantValidationService(),
  merchantCreationService: () => createPayrixMerchantCreationService(),
  noteManagementService: () => createPayrixNoteManagementService(),
  userService: () => ({
    createUserAccount: (userData) => createPayrixUserAccount(userData),
  }),
  paymentService: () => createPayrixPaymentService(),
  merchantService: () => createPayrixMerchantService(),
};

export function createServiceContainer(customFactories: Partial<ServiceFactories> = {}): ServiceContainer {
  const factories = { ...defaultServiceFactories, ...customFactories };

  let merchantValidationService: IPayrixMerchantValidationService | undefined;
  let merchantCreationService: IPayrixMerchantCreationService | undefined;
  let noteManagementService: IPayrixNoteManagementService | undefined;
  let userService: IPayrixUserService | undefined;
  let paymentService: IPayrixPaymentService | undefined;
  let merchantService: IPayrixMerchantService | undefined;

  return {
    getMerchantValidationService: () => {
      if (!merchantValidationService) {
        merchantValidationService = factories.merchantValidationService();
      }
      return merchantValidationService;
    },
    getMerchantCreationService: () => {
      if (!merchantCreationService) {
        merchantCreationService = factories.merchantCreationService();
      }
      return merchantCreationService;
    },
    getNoteManagementService: () => {
      if (!noteManagementService) {
        noteManagementService = factories.noteManagementService();
      }
      return noteManagementService;
    },
    getUserService: () => {
      if (!userService) {
        userService = factories.userService();
      }
      return userService;
    },
    getPaymentService: () => {
      if (!paymentService) {
        paymentService = factories.paymentService();
      }
      return paymentService;
    },
    getMerchantService: () => {
      if (!merchantService) {
        merchantService = factories.merchantService();
      }
      return merchantService;
    },

    setServices: (services: Partial<ServiceRegistry>) => {
      if (services.merchantValidationService) {
        merchantValidationService = services.merchantValidationService;
      }
      if (services.merchantCreationService) {
        merchantCreationService = services.merchantCreationService;
      }
      if (services.noteManagementService) {
        noteManagementService = services.noteManagementService;
      }
      if (services.userService) {
        userService = services.userService;
      }
      if (services.paymentService) {
        paymentService = services.paymentService;
      }
      if (services.merchantService) {
        merchantService = services.merchantService;
      }
    },

    reset: () => {
      merchantValidationService = undefined;
      merchantCreationService = undefined;
      noteManagementService = undefined;
      userService = undefined;
      paymentService = undefined;
      merchantService = undefined;
    },
  };
}

let globalServiceContainer: ServiceContainer | null = null;

export function getServiceContainer(): ServiceContainer {
  if (!globalServiceContainer) {
    globalServiceContainer = createServiceContainer();
  }
  return globalServiceContainer;
}

export function setServiceContainer(container: ServiceContainer): void {
  globalServiceContainer = container;
}

export function resetServiceContainer(): void {
  globalServiceContainer = null;
}
