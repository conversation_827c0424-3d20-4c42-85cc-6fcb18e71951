import { type OnboardingRequest } from "../functions/merchants/schemas/onboarding.schema.js";
import { createPayrixMerchantService, type PayrixMerchantServiceInterface } from "./payrix/merchant.service.js";
import { createPayrixUserAccount, type PayrixUserServiceDependencies } from "./payrix/user.service.js";
import { createPayrixPaymentService, type PayrixPaymentServiceInterface } from "./payrix/payment.service.js";
import {
  PayrixMerchantResponse,
  PayrixUserResponse,
  TokenPaymentData,
  UserAccountData,
  PaymentResult,
  MerchantValidationResult,
  TokenDeletionResult,
} from "../types/payrix.types.js";

export interface PayrixServiceDependencies {
  merchantService?: PayrixMerchantServiceInterface;
  userServiceDependencies?: PayrixUserServiceDependencies;
  paymentService?: PayrixPaymentServiceInterface;
}

export interface PayrixServiceInterface {
  checkMerchantExists(email: string, ein?: string): Promise<boolean>;
  validateMerchantById(merchantId: string): Promise<MerchantValidationResult>;
  createMerchant(merchantData: OnboardingRequest): Promise<PayrixMerchantResponse>;
  createUserAccount(userData: UserAccountData): Promise<PayrixUserResponse>;
  processTokenPayment(paymentData: TokenPaymentData): Promise<PaymentResult>;
  deleteToken(token: string): Promise<TokenDeletionResult>;
  createNote(noteData: { entity: string; note: string; type?: string; login?: string }): Promise<{ id: string }>;
  createNoteDocument(documentData: {
    note: string;
    file: { filename: string; content: Buffer; contentType: string };
    description?: string;
  }): Promise<{ id: string }>;
  createPlaidLinkToken(linkTokenData: {
    userId: string;
    countryCode: string;
    redirectUri: string;
  }): Promise<{ linkToken: string; requestId: string }>;
}

export function createPayrixService(dependencies: PayrixServiceDependencies = {}): PayrixServiceInterface {
  const merchantService = dependencies.merchantService || createPayrixMerchantService();
  const userServiceDependencies = dependencies.userServiceDependencies || {};
  const paymentService = dependencies.paymentService || createPayrixPaymentService();

  return {
    checkMerchantExists: (email: string, ein?: string) => merchantService.checkMerchantExists(email, ein),

    validateMerchantById: (merchantId: string) => merchantService.validateMerchantById(merchantId),

    createMerchant: (merchantData: OnboardingRequest) => merchantService.createMerchant(merchantData),

    createUserAccount: (userData: UserAccountData) => createPayrixUserAccount(userData, userServiceDependencies),

    processTokenPayment: (paymentData: TokenPaymentData) => paymentService.processTokenPayment(paymentData),

    deleteToken: (token: string) => paymentService.deleteToken(token),

    createNote: (noteData: { entity: string; note: string; type?: string; login?: string }) => merchantService.createNote(noteData),

    createNoteDocument: (documentData: { note: string; file: { filename: string; content: Buffer; contentType: string }; description?: string }) =>
      merchantService.createNoteDocument(documentData),

    createPlaidLinkToken: (linkTokenData: { userId: string; countryCode: string; redirectUri: string }) =>
      merchantService.createPlaidLinkToken(linkTokenData),
  };
}

// Export the functional service as the default export
export const payrixService = createPayrixService();
