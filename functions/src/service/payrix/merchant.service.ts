import { type OnboardingRequest } from "../../functions/merchants/schemas/onboarding.schema.js";
import { PayrixMerchantResponse, MerchantValidationResult } from "../../types/payrix.types.js";
import { payrixMerchantValidationService, type PayrixMerchantValidationServiceInterface } from "./merchant-validation.service.js";
import { payrixMerchantCreationService, type PayrixMerchantCreationServiceInterface } from "./merchant-creation.service.js";
import { payrixNoteManagementService, type PayrixNoteManagementServiceInterface } from "./note-management.service.js";

export interface PayrixMerchantServiceDependencies {
  validationService?: PayrixMerchantValidationServiceInterface;
  creationService?: PayrixMerchantCreationServiceInterface;
  noteService?: PayrixNoteManagementServiceInterface;
}

export interface PayrixMerchantServiceInterface {
  checkMerchantExists(email: string, ein?: string): Promise<boolean>;
  validateMerchantById(merchantId: string): Promise<MerchantValidationResult>;
  createMerchant(merchantData: OnboardingRequest): Promise<PayrixMerchantResponse>;
  createNote(noteData: { entity: string; note: string; type?: string; login?: string }): Promise<{ id: string }>;
  createPlaidLinkToken(linkTokenData: {
    userId: string;
    countryCode: string;
    redirectUri: string;
  }): Promise<{ linkToken: string; requestId: string }>;
  createNoteDocument(documentData: {
    note: string;
    file: { filename: string; content: Buffer; contentType: string };
    description?: string;
  }): Promise<{ id: string }>;
}

export function createPayrixMerchantService(dependencies: PayrixMerchantServiceDependencies = {}): PayrixMerchantServiceInterface {
  const validationService = dependencies.validationService || payrixMerchantValidationService;
  const creationService = dependencies.creationService || payrixMerchantCreationService;
  const noteService = dependencies.noteService || payrixNoteManagementService;

  return {
    checkMerchantExists: (email: string, ein?: string) => validationService.checkMerchantExists(email, ein),

    validateMerchantById: (merchantId: string) => validationService.validateMerchantById(merchantId),

    createMerchant: (merchantData: OnboardingRequest) => creationService.createMerchant(merchantData),

    createNote: (noteData: { entity: string; note: string; type?: string; login?: string }) => noteService.createNote(noteData),

    createPlaidLinkToken: (linkTokenData: { userId: string; countryCode: string; redirectUri: string }) =>
      creationService.createPlaidLinkToken(linkTokenData),

    createNoteDocument: (documentData: { note: string; file: { filename: string; content: Buffer; contentType: string }; description?: string }) =>
      noteService.createNoteDocument(documentData),
  };
}

// Export the functional service as the default export
export const payrixMerchantService = createPayrixMerchantService();
